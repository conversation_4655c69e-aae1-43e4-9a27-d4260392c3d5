cmake_minimum_required(VERSION 3.16)
project(CS2_SkinChanger)

# Definir padrão C++
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Configurações para Windows
if(WIN32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /MT")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
endif()

# Arquivos fonte
set(SOURCES
    main_active_only.cpp
)

# Arquivos header
set(HEADERS
    memory.h
    offsets.h
    skins.h
)

# Criar executável
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Configurações específicas do Windows
if(WIN32)
    target_compile_definitions(${PROJECT_NAME} PRIVATE 
        WIN32_LEAN_AND_MEAN
        NOMINMAX
        _CRT_SECURE_NO_WARNINGS
    )
    
    # Definir como aplicação de console
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE FALSE
    )
endif()

# Configurações de compilação
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W3
        /permissive-
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall
        -Wextra
        -pedantic
    )
endif()

# Configurações de Release
set_target_properties(${PROJECT_NAME} PROPERTIES
    OUTPUT_NAME "CS2_SkinChanger"
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)
