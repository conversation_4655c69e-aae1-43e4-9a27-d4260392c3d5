#include "memory.h"
#include "offsets.h"
#include "skins.h"
#include <thread>
#include <iostream>
#include <array>
#include <chrono>
#include <algorithm>
#include <string>

using namespace std;

// Funções de ofuscação (mantidas do código original)
int garb1(int x)
{
	int y = x;
	y = 10 - 10;
	x = y - 293;
	return x;
}

float randomFloat() {
	return (float)rand() / RAND_MAX;
}

int stringToInt(std::string str) {
	int result = 0;
	for (int i = 0; i < str.length(); i++) {
		result += (int)str[i];
	}
	return result;
}

int** addMatrices(int** matrix1, int** matrix2, int rows, int cols) {
	int** result = new int* [rows];
	for (int i = 0; i < rows; i++) {
		result[i] = new int[cols];
		for (int j = 0; j < cols; j++) {
			result[i][j] = matrix1[i][j] + matrix2[i][j];
		}
	}
	return result;
}

int factorial(int n) {
	if (n <= 1) {
		return 1;
	}
	return n * factorial(n - 1);
}

std::string GetWeaponName(const Memory& memory, uintptr_t localPlayer, uintptr_t weapon)
{
	try
	{
		// Método correto: player -> m_pClippingWeapon -> CBasePlayerWeaponVData -> name
		const auto clippingWeapon = memory.Read<uintptr_t>(localPlayer + offset::m_pClippingWeapon);
		if (!clippingWeapon || clippingWeapon < 0x1000) return "invalid_clipping";

		const auto weaponData = memory.Read<uintptr_t>(clippingWeapon + 0x360);
		if (!weaponData || weaponData < 0x1000) return "invalid_data";

		const auto weaponNameAddress = memory.Read<uintptr_t>(weaponData + 0xc18);
		if (!weaponNameAddress || weaponNameAddress < 0x1000) return "invalid_name_addr";

		// Ler string do nome da arma
		char weaponName[32] = { 0 };
		for (int i = 0; i < 31; i++)
		{
			char c = memory.Read<char>(weaponNameAddress + i);
			if (c == 0) break;
			if (c < 32 || c > 126) break; // Caracteres válidos ASCII
			weaponName[i] = c;
		}

		std::string name(weaponName);

		// Verificar se contém "weapon_" e remover prefixo
		if (name.find("weapon_") == 0 && name.length() > 7)
		{
			return name.substr(7); // Remover prefixo "weapon_"
		}

		// Se não tem prefixo weapon_, retornar como está
		return name.empty() ? "unknown" : name;
	}
	catch (...)
	{
		return "error";
	}
}

// Função auxiliar para obter nome da arma baseado no item definition index
std::string GetWeaponNameFromID(uint16_t itemDefIndex)
{
	switch (itemDefIndex)
	{
		case 1: return "deagle";
		case 2: return "elite";
		case 3: return "fiveseven";
		case 4: return "glock";
		case 7: return "ak47";
		case 8: return "aug";
		case 9: return "awp";
		case 10: return "famas";
		case 11: return "g3sg1";
		case 13: return "galilar";
		case 14: return "m249";
		case 16: return "m4a4";
		case 17: return "mac10";
		case 19: return "p90";
		case 24: return "ump45";
		case 25: return "xm1014";
		case 26: return "bizon";
		case 27: return "mag7";
		case 28: return "negev";
		case 29: return "sawedoff";
		case 30: return "tec9";
		case 31: return "taser";
		case 32: return "hkp2000";
		case 33: return "mp7";
		case 34: return "mp9";
		case 35: return "nova";
		case 36: return "p250";
		case 38: return "scar20";
		case 39: return "sg556";
		case 40: return "ssg08";
		case 42: return "knife";
		case 59: return "knife_t";
		case 60: return "m4a1_silencer";
		case 61: return "usp_silencer";
		case 63: return "cz75a";
		case 64: return "revolver";
		case 71: return "utility";
		case 132: return "utility";
		case 322: return "knife_special";
		default: return "unknown_id_" + std::to_string(itemDefIndex);
	}
}

// Função auxiliar para obter nome da arma (método alternativo)
std::string GetWeaponNameAlt(const Memory& memory, uintptr_t weapon)
{
	try
	{
		// Método alternativo: weapon + designername (0x20)
		const auto designerNamePtr = memory.Read<uintptr_t>(weapon + 0x20);
		if (!designerNamePtr || designerNamePtr < 0x1000) return "invalid_designer";

		// Ler string do nome da arma
		char weaponName[64] = { 0 };
		for (int i = 0; i < 63; i++)
		{
			char c = memory.Read<char>(designerNamePtr + i);
			if (c == 0) break;
			if (c < 32 || c > 126) break; // Caracteres válidos ASCII
			weaponName[i] = c;
		}

		std::string name(weaponName);

		// Verificar se contém "weapon_" e remover prefixo
		if (name.find("weapon_") == 0 && name.length() > 7)
		{
			return name.substr(7);
		}

		return name.empty() ? "unknown_alt" : name;
	}
	catch (...)
	{
		return "error_alt";
	}
}

void ForceFullUpdate(const Memory& memory, uintptr_t engine)
{
	const auto networkGameClient = memory.Read<uintptr_t>(engine + offset::dwNetworkGameClient);
	cout << "[DEBUG] NetworkGameClient: 0x" << hex << networkGameClient << endl;

	if (networkGameClient && networkGameClient > 0x1000)
	{
		const auto deltaTick = memory.Read<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick);
		cout << "[DEBUG] Current delta tick: " << dec << deltaTick << endl;

		memory.Write<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick, -1);
		cout << "[DEBUG] Forced full update by setting delta tick to -1." << endl;
	}
	else
	{
		cout << "[ERROR] Invalid NetworkGameClient address" << endl;
	}
}

void ForceViewModelUpdate(const Memory& memory, uintptr_t client, uintptr_t localPlayer)
{
	const auto viewModelServices = memory.Read<uintptr_t>(localPlayer + offset::m_pViewModelServices);
	if (!viewModelServices) return;

	const auto viewHandle = memory.Read<uintptr_t>(viewModelServices + offset::m_hViewModel);
	if (!viewHandle) return;

	const auto viewListEntry = memory.Read<uintptr_t>(
		client + offset::dwEntityList + (0x8 * ((viewHandle & 0x7FFF) >> 9)) + 16);
	if (!viewListEntry) return;

	const auto viewController = memory.Read<uintptr_t>(viewListEntry + 120 * (viewHandle & 0x1FF));
	if (!viewController) return;

	const auto viewNode = memory.Read<uintptr_t>(viewController + 0x318);
	if (!viewNode) return;

	const auto viewMask = memory.Read<uint64_t>(viewNode + 0x160);
	if (viewMask != 2)
	{
		memory.Write<uint64_t>(viewNode + 0x160, 2);
		cout << "[DEBUG] Forced ViewModel update." << endl;
	}
}

bool ApplySkinToWeapon(const Memory& memory, uintptr_t weapon, short itemDefIndex)
{
	const auto skinConfig = GetWeaponSkin(itemDefIndex);
	if (skinConfig.paintKit == 0)
	{
		cout << "[DEBUG] No skin configuration found for weapon ID: " << itemDefIndex << endl;
		return false;
	}

	cout << "[DEBUG] Applying skin to weapon ID " << itemDefIndex
		 << " - Paint Kit: " << skinConfig.paintKit << endl;

	// Verificar se a skin já está aplicada
	const auto currentPaintKit = memory.Read<int32_t>(weapon + offset::m_nFallbackPaintKit);
	if (currentPaintKit == skinConfig.paintKit)
	{
		cout << "[DEBUG] Skin already applied (Paint Kit: " << currentPaintKit << ")" << endl;
		return false;
	}

	cout << "[DEBUG] Current paint kit: " << currentPaintKit << ", applying new: " << skinConfig.paintKit << endl;

	// Aplicar configurações da skin usando offsets corretos
	try
	{
		// Ler AttributeManager para aplicar as skins corretamente
		const auto attributeManager = memory.Read<uintptr_t>(weapon + offset::m_AttributeManager);
		if (attributeManager && attributeManager > 0x1000)
		{
			const auto item = memory.Read<uintptr_t>(attributeManager + offset::m_Item);
			if (item && item > 0x1000)
			{
				// Aplicar via AttributeManager -> Item
				memory.Write<int32_t>(item + offset::m_iItemIDHigh, -1);
				memory.Write<int32_t>(item + offset::m_iAccountID, memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow));
			}
		}

		// Aplicar fallback values diretamente na arma (C_EconEntity)
		memory.Write<int32_t>(weapon + offset::m_iItemIDHigh, -1);
		memory.Write<int32_t>(weapon + offset::m_nFallbackPaintKit, skinConfig.paintKit);
		memory.Write<float>(weapon + offset::m_flFallbackWear, skinConfig.wear);
		memory.Write<int32_t>(weapon + offset::m_nFallbackStatTrak, skinConfig.statTrak);
		memory.Write<int32_t>(weapon + offset::m_nFallbackSeed, skinConfig.seed);

		// Definir AccountID
		const auto originalOwner = memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow);
		memory.Write<int32_t>(weapon + offset::m_iAccountID, originalOwner);

		cout << "[DEBUG] Skin configuration applied successfully!" << endl;
		return true;
	}
	catch (...)
	{
		cout << "[ERROR] Exception occurred while applying skin" << endl;
		return false;
	}
}

int main()
{
	cout << "=== CS2 External Skin Changer ===" << endl;
	cout << "Desenvolvido com offsets atualizados para 2025-07-08" << endl;
	cout << "Pressione Enter para iniciar..." << endl;
	cin.get();

	cout << "[DEBUG] Inicializando memoria..." << endl;
	const auto memory = Memory("cs2.exe");

	cout << "[DEBUG] Obtendo enderecos dos modulos..." << endl;
	const auto client = memory.GetModuleAddress("client.dll");
	const auto engine = memory.GetModuleAddress("engine2.dll");

	if (!client || !engine)
	{
		cout << "[ERROR] Falha ao obter endereco dos modulos." << endl;
		cout << "Certifique-se de que o CS2 esta rodando." << endl;
		cin.get();
		return 0;
	}

	cout << "[DEBUG] Client base: 0x" << hex << client << endl;
	cout << "[DEBUG] Engine base: 0x" << hex << engine << endl;

	cout << "[INFO] Skin Changer iniciado! Pressione Ctrl+C para parar." << endl;

	while (true)
	{
		try
		{
			const auto localPlayer = memory.Read<uintptr_t>(client + offset::dwLocalPlayerPawn);
			if (!localPlayer)
			{
				cout << "[DEBUG] Local player not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Local player found: 0x" << hex << localPlayer << endl;

			const auto weaponServices = memory.Read<uintptr_t>(localPlayer + offset::m_pWeaponServices);
			if (!weaponServices)
			{
				cout << "[DEBUG] Weapon services not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Weapon services found: 0x" << hex << weaponServices << endl;

			// Ler armas do jogador - C_NetworkUtlVectorBase structure
			// Estrutura: size (0x0), data pointer (0x8)
			const auto weaponCount = memory.Read<int>(weaponServices + offset::m_hMyWeapons + 0x0); // size
			const auto weaponsArray = memory.Read<uintptr_t>(weaponServices + offset::m_hMyWeapons + 0x8); // data pointer

			cout << "[DEBUG] Weapon count: " << dec << weaponCount << endl;
			cout << "[DEBUG] Weapons array: 0x" << hex << weaponsArray << endl;

			if (!weaponsArray || weaponCount <= 0 || weaponCount > 64)
			{
				cout << "[DEBUG] Invalid weapons data, skipping..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			array<uintptr_t, 64> weapons{};
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				weapons[i] = memory.Read<uintptr_t>(weaponsArray + (i * 0x4));
			}

			bool needsUpdate = false;
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				const auto handle = weapons[i];
				if (!handle) continue;

				cout << "[DEBUG] Processing weapon handle: 0x" << hex << handle << endl;

				// Calcular endereco da arma usando a estrutura de entity list correta
				const auto listEntry = (handle & 0x7FFF) >> 9;
				const auto listIndex = handle & 0x1FF;

				const auto weaponListEntry = memory.Read<uintptr_t>(
					client + offset::dwEntityList + (0x8 * listEntry) + 0x10);
				if (!weaponListEntry)
				{
					cout << "[DEBUG] Invalid weapon list entry for handle: 0x" << hex << handle << endl;
					continue;
				}

				// Usar o offset correto para a entidade (0x78 = 120 bytes por entrada)
				const auto weapon = memory.Read<uintptr_t>(weaponListEntry + 120 * listIndex);
				if (!weapon)
				{
					cout << "[DEBUG] Invalid weapon entity for handle: 0x" << hex << handle << endl;
					continue;
				}

				cout << "[DEBUG] Weapon entity: 0x" << hex << weapon << endl;

				// Validar se o endereço da arma é válido
				if (weapon < 0x1000 || weapon > 0x7FFFFFFFFFFF)
				{
					cout << "[DEBUG] Invalid weapon address: 0x" << hex << weapon << endl;
					continue;
				}

				// Método 1: Tentar ler diretamente do AttributeManager da arma
				const auto attributeManager = memory.Read<uintptr_t>(weapon + offset::m_AttributeManager);
				cout << "[DEBUG] AttributeManager: 0x" << hex << attributeManager << endl;

				uint16_t itemDefIndex = 0;
				bool foundValidIndex = false;

				// Verificar se o AttributeManager parece válido (endereço dentro de faixas razoáveis)
				if (attributeManager &&
					attributeManager > 0x100000 &&
					attributeManager < 0x7FF000000000 &&
					(attributeManager & 0xFFF) == 0) // Deve estar alinhado
				{
					const auto item = memory.Read<uintptr_t>(attributeManager + offset::m_Item);
					cout << "[DEBUG] Item: 0x" << hex << item << endl;

					if (item &&
						item > 0x100000 &&
						item < 0x7FF000000000)
					{
						itemDefIndex = memory.Read<uint16_t>(item + offset::m_iItemDefinitionIndex);
						cout << "[DEBUG] Method 1 - Item definition index: " << dec << itemDefIndex << endl;

						// Validação mais restritiva para IDs de armas válidas
						if (itemDefIndex > 0 && itemDefIndex < 1000)
						{
							foundValidIndex = true;
						}
					}
				}
				else
				{
					cout << "[DEBUG] AttributeManager address seems invalid, skipping method 1" << endl;
				}

				// Método 2: Tentar ler diretamente de offsets conhecidos na arma
				if (!foundValidIndex)
				{
					// Array de offsets conhecidos onde o item definition index pode estar
					std::array<std::ptrdiff_t, 5> directOffsets = {
						0x1302,  // Offset direto comum
						0x1BA,   // Offset do EconItemView
						0x11F2,  // Offset alternativo 1
						0x1352,  // Offset alternativo 2
						0x16E2   // Offset alternativo 3
					};

					for (size_t i = 0; i < directOffsets.size(); i++)
					{
						const auto directItemDefIndex = memory.Read<uint16_t>(weapon + directOffsets[i]);
						cout << "[DEBUG] Method 2." << (i+1) << " - Direct item definition index: " << dec << directItemDefIndex << endl;

						if (directItemDefIndex > 0 && directItemDefIndex < 1000)
						{
							itemDefIndex = directItemDefIndex;
							foundValidIndex = true;
							cout << "[DEBUG] Found valid ID using direct offset 0x" << hex << directOffsets[i] << endl;
							break;
						}
					}
				}

				// Método 3: Scan pela estrutura da arma procurando padrões válidos
				if (!foundValidIndex)
				{
					cout << "[DEBUG] Method 3 - Scanning weapon structure for valid item definition index..." << endl;

					// Fazer scan em uma faixa de offsets procurando valores válidos
					for (std::ptrdiff_t offset = 0x1000; offset < 0x2000; offset += 0x2)
					{
						try
						{
							const auto scanItemDefIndex = memory.Read<uint16_t>(weapon + offset);

							// Verificar se é um ID de arma válido conhecido
							if (scanItemDefIndex > 0 && scanItemDefIndex < 1000)
							{
								// Verificar se é um dos IDs conhecidos de armas
								bool isKnownWeapon = false;
								std::array<uint16_t, 20> knownWeaponIds = {
									1, 2, 3, 4, 7, 8, 9, 10, 11, 13, 14, 16, 17, 19, 24, 25, 26, 27, 28, 29
								};

								for (auto knownId : knownWeaponIds)
								{
									if (scanItemDefIndex == knownId)
									{
										isKnownWeapon = true;
										break;
									}
								}

								if (isKnownWeapon)
								{
									itemDefIndex = scanItemDefIndex;
									foundValidIndex = true;
									cout << "[DEBUG] Method 3 - Found valid weapon ID " << dec << scanItemDefIndex
										 << " at offset 0x" << hex << offset << endl;
									break;
								}
							}
						}
						catch (...)
						{
							// Ignorar erros de leitura e continuar
							continue;
						}
					}
				}
				// Processar apenas se encontramos um índice válido
				if (foundValidIndex)
				{
					// Usar nome baseado no ID para maior confiabilidade
					const auto weaponName = GetWeaponNameFromID(itemDefIndex);
					cout << "[DEBUG] Valid weapon found - ID: " << dec << itemDefIndex
						 << ", Name: " << weaponName << endl;

					if (ApplySkinToWeapon(memory, weapon, itemDefIndex))
					{
						needsUpdate = true;
						cout << "[DEBUG] Skin applied successfully to " << weaponName
							 << " (ID: " << itemDefIndex << ")" << endl;
					}
				}
				else
				{
					// Tentar obter nome alternativo para debug
					const auto weaponNameAlt = GetWeaponNameAlt(memory, weapon);
					cout << "[DEBUG] No valid item definition index found for weapon: "
						 << weaponNameAlt << " (0x" << hex << weapon << ")" << endl;
				}
			}

			// Forcar atualizacao se necessario
			if (needsUpdate)
			{
				ForceViewModelUpdate(memory, client, localPlayer);
				ForceFullUpdate(memory, engine);
			}

			this_thread::sleep_for(chrono::milliseconds(500));
		}
		catch (const exception& e)
		{
			cout << "[ERROR] Excecao capturada: " << e.what() << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
		}
	}

	return 0;
}
