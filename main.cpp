#include "memory.h"
#include "offsets.h"
#include "skins.h"
#include <thread>
#include <iostream>
#include <array>
#include <chrono>
#include <algorithm>
#include <string>

using namespace std;

// Funções de ofuscação (mantidas do código original)
int garb1(int x)
{
	int y = x;
	y = 10 - 10;
	x = y - 293;
	return x;
}

float randomFloat() {
	return (float)rand() / RAND_MAX;
}

int stringToInt(std::string str) {
	int result = 0;
	for (int i = 0; i < str.length(); i++) {
		result += (int)str[i];
	}
	return result;
}

int** addMatrices(int** matrix1, int** matrix2, int rows, int cols) {
	int** result = new int* [rows];
	for (int i = 0; i < rows; i++) {
		result[i] = new int[cols];
		for (int j = 0; j < cols; j++) {
			result[i][j] = matrix1[i][j] + matrix2[i][j];
		}
	}
	return result;
}

int factorial(int n) {
	if (n <= 1) {
		return 1;
	}
	return n * factorial(n - 1);
}

std::string GetWeaponName(const Memory& memory, uintptr_t localPlayer, uintptr_t weapon)
{
	try
	{
		// Método correto: player -> m_pClippingWeapon -> CBasePlayerWeaponVData -> name
		const auto clippingWeapon = memory.Read<uintptr_t>(localPlayer + offset::m_pClippingWeapon);
		if (!clippingWeapon || clippingWeapon < 0x1000) return "invalid_clipping";

		const auto weaponData = memory.Read<uintptr_t>(clippingWeapon + 0x360);
		if (!weaponData || weaponData < 0x1000) return "invalid_data";

		const auto weaponNameAddress = memory.Read<uintptr_t>(weaponData + 0xc18);
		if (!weaponNameAddress || weaponNameAddress < 0x1000) return "invalid_name_addr";

		// Ler string do nome da arma
		char weaponName[32] = { 0 };
		for (int i = 0; i < 31; i++)
		{
			char c = memory.Read<char>(weaponNameAddress + i);
			if (c == 0) break;
			if (c < 32 || c > 126) break; // Caracteres válidos ASCII
			weaponName[i] = c;
		}

		std::string name(weaponName);

		// Verificar se contém "weapon_" e remover prefixo
		if (name.find("weapon_") == 0 && name.length() > 7)
		{
			return name.substr(7); // Remover prefixo "weapon_"
		}

		// Se não tem prefixo weapon_, retornar como está
		return name.empty() ? "unknown" : name;
	}
	catch (...)
	{
		return "error";
	}
}

// Função auxiliar para obter nome da arma baseado no item definition index
std::string GetWeaponNameFromID(uint16_t itemDefIndex)
{
	switch (itemDefIndex)
	{
		case 1: return "deagle";
		case 2: return "elite";
		case 3: return "fiveseven";
		case 4: return "glock";
		case 7: return "ak47";
		case 8: return "aug";
		case 9: return "awp";
		case 10: return "famas";
		case 11: return "g3sg1";
		case 13: return "galilar";
		case 14: return "m249";
		case 16: return "m4a4";
		case 17: return "mac10";
		case 19: return "p90";
		case 24: return "ump45";
		case 25: return "xm1014";
		case 26: return "bizon";
		case 27: return "mag7";
		case 28: return "negev";
		case 29: return "sawedoff";
		case 30: return "tec9";
		case 31: return "taser";
		case 32: return "hkp2000";
		case 33: return "mp7";
		case 34: return "mp9";
		case 35: return "nova";
		case 36: return "p250";
		case 38: return "scar20";
		case 39: return "sg556";
		case 40: return "ssg08";
		case 42: return "knife";
		case 59: return "knife_t";
		case 60: return "m4a1_silencer";
		case 61: return "usp_silencer";
		case 63: return "cz75a";
		case 64: return "revolver";
		case 71: return "utility";
		case 132: return "utility";
		case 322: return "knife_special";
		case 368: return "ak47"; // AK-47 CS2 ID
		case 376: return "weapon_376";
		case 480: return "weapon_480";
		default: return "unknown_id_" + std::to_string(itemDefIndex);
	}
}

// Função para identificar a arma pelo nome real
std::string IdentifyWeaponByName(const Memory& memory, uintptr_t localPlayer, uintptr_t weapon)
{
	try
	{
		// Método 1: Tentar ler o nome da arma usando clipping weapon
		const auto clippingWeapon = memory.Read<uintptr_t>(localPlayer + offset::m_pClippingWeapon);
		if (clippingWeapon && clippingWeapon > 0x1000)
		{
			const auto weaponData = memory.Read<uintptr_t>(clippingWeapon + 0x360);
			if (weaponData && weaponData > 0x1000)
			{
				const auto weaponNameAddress = memory.Read<uintptr_t>(weaponData + 0xc18);
				if (weaponNameAddress && weaponNameAddress > 0x1000)
				{
					char weaponName[64] = { 0 };
					for (int i = 0; i < 63; i++)
					{
						char c = memory.Read<char>(weaponNameAddress + i);
						if (c == 0) break;
						if (c < 32 || c > 126) break;
						weaponName[i] = c;
					}

					std::string name(weaponName);
					if (!name.empty() && name != "invalid_name_addr")
					{
						// Remover prefixo "weapon_" se existir
						if (name.find("weapon_") == 0 && name.length() > 7)
						{
							return name.substr(7);
						}
						return name;
					}
				}
			}
		}

		// Método 2: Tentar outros offsets conhecidos para nome
		std::array<std::ptrdiff_t, 3> nameOffsets = { 0x20, 0x28, 0x30 };
		for (auto offset : nameOffsets)
		{
			const auto namePtr = memory.Read<uintptr_t>(weapon + offset);
			if (namePtr && namePtr > 0x1000)
			{
				char weaponName[64] = { 0 };
				for (int i = 0; i < 63; i++)
				{
					char c = memory.Read<char>(namePtr + i);
					if (c == 0) break;
					if (c < 32 || c > 126) break;
					weaponName[i] = c;
				}

				std::string name(weaponName);
				if (!name.empty() && name.find("weapon_") != std::string::npos)
				{
					if (name.find("weapon_") == 0 && name.length() > 7)
					{
						return name.substr(7);
					}
					return name;
				}
			}
		}

		return "unknown_weapon";
	}
	catch (...)
	{
		return "error_weapon";
	}
}

// Função para mapear nome da arma para ID correto
uint16_t GetCorrectWeaponID(const std::string& weaponName)
{
	if (weaponName == "ak47") return 7;
	if (weaponName == "m4a1" || weaponName == "m4a4") return 16;
	if (weaponName == "awp") return 9;
	if (weaponName == "deagle") return 1;
	if (weaponName == "glock") return 2;
	if (weaponName == "usp" || weaponName == "usp_silencer") return 61;
	if (weaponName == "knife" || weaponName == "knife_t") return 42;
	if (weaponName == "famas") return 10;
	if (weaponName == "galil" || weaponName == "galilar") return 13;
	if (weaponName == "p250") return 36;
	if (weaponName == "tec9") return 30;
	if (weaponName == "elite") return 2;
	if (weaponName == "fiveseven") return 3;
	if (weaponName == "cz75a") return 63;
	if (weaponName == "revolver") return 64;

	return 0; // Desconhecido
}

void ForceFullUpdate(const Memory& memory, uintptr_t engine)
{
	const auto networkGameClient = memory.Read<uintptr_t>(engine + offset::dwNetworkGameClient);
	cout << "[DEBUG] NetworkGameClient: 0x" << hex << networkGameClient << endl;

	if (networkGameClient && networkGameClient > 0x1000)
	{
		const auto deltaTick = memory.Read<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick);
		cout << "[DEBUG] Current delta tick: " << dec << deltaTick << endl;

		memory.Write<int32_t>(networkGameClient + offset::dwNetworkGameClient_deltaTick, -1);
		cout << "[DEBUG] Forced full update by setting delta tick to -1." << endl;
	}
	else
	{
		cout << "[ERROR] Invalid NetworkGameClient address" << endl;
	}
}

void ForceViewModelUpdate(const Memory& memory, uintptr_t client, uintptr_t localPlayer)
{
	try
	{
		const auto viewModelServices = memory.Read<uintptr_t>(localPlayer + offset::m_pViewModelServices);
		if (!viewModelServices)
		{
			cout << "[DEBUG] No ViewModel services found" << endl;
			return;
		}

		const auto viewHandle = memory.Read<uint32_t>(viewModelServices + offset::m_hViewModel);
		if (!viewHandle)
		{
			cout << "[DEBUG] No ViewModel handle found" << endl;
			return;
		}

		cout << "[DEBUG] ViewModel handle: 0x" << hex << viewHandle << endl;

		// Usar a mesma lógica de CHandle do guia UC
		const auto handle = viewHandle & 0x7FFF;
		const auto listEntry = handle >> 9;
		const auto listIndex = handle & 0x1FF;

		const auto viewListEntry = memory.Read<uintptr_t>(
			client + offset::dwEntityList + 8 * listEntry + 16);
		if (!viewListEntry)
		{
			cout << "[DEBUG] No ViewModel list entry found" << endl;
			return;
		}

		const auto viewModel = memory.Read<uintptr_t>(viewListEntry + 120 * listIndex);
		if (!viewModel)
		{
			cout << "[DEBUG] No ViewModel entity found" << endl;
			return;
		}

		cout << "[DEBUG] ViewModel entity: 0x" << hex << viewModel << endl;

		const auto viewGameSceneNode = memory.Read<uintptr_t>(viewModel + offset::m_pGameSceneNode);
		if (!viewGameSceneNode)
		{
			cout << "[DEBUG] No ViewModel GameSceneNode found" << endl;
			return;
		}

		const auto viewMeshGroupMask = memory.Read<int32_t>(viewGameSceneNode + 0x160 + offset::m_MeshGroupMask);
		cout << "[DEBUG] Current ViewModel MeshGroupMask: " << dec << viewMeshGroupMask << endl;

		if (viewMeshGroupMask != 2)
		{
			memory.Write<int32_t>(viewGameSceneNode + 0x160 + offset::m_MeshGroupMask, 2);
			cout << "[DEBUG] Set ViewModel MeshGroupMask to 2" << endl;
		}
		else
		{
			cout << "[DEBUG] ViewModel MeshGroupMask already set to 2" << endl;
		}
	}
	catch (...)
	{
		cout << "[ERROR] Exception in ForceViewModelUpdate" << endl;
	}
}

bool ApplySkinToWeapon(const Memory& memory, uintptr_t weapon, short itemDefIndex)
{
	const auto skinConfig = GetWeaponSkin(itemDefIndex);
	if (skinConfig.paintKit == 0)
	{
		cout << "[DEBUG] No skin configuration found for weapon ID: " << itemDefIndex << endl;
		return false;
	}

	cout << "[DEBUG] Applying skin to weapon ID " << itemDefIndex
		 << " - Paint Kit: " << skinConfig.paintKit << endl;

	// Verificar se a skin já está aplicada
	const auto currentPaintKit = memory.Read<int32_t>(weapon + offset::m_nFallbackPaintKit);
	if (currentPaintKit == skinConfig.paintKit)
	{
		cout << "[DEBUG] Skin already applied (Paint Kit: " << currentPaintKit << ")" << endl;
		return false;
	}

	cout << "[DEBUG] Current paint kit: " << currentPaintKit << ", applying new: " << skinConfig.paintKit << endl;

	try
	{
		// 1. Primeiro, configurar o MeshGroupMask (crucial para skins funcionarem)
		const auto weaponGameSceneNode = memory.Read<uintptr_t>(weapon + offset::m_pGameSceneNode);
		if (weaponGameSceneNode && weaponGameSceneNode > 0x1000)
		{
			const auto currentMeshGroupMask = memory.Read<int32_t>(weaponGameSceneNode + 0x160 + offset::m_MeshGroupMask);
			if (currentMeshGroupMask != -1)
			{
				memory.Write<int32_t>(weaponGameSceneNode + 0x160 + offset::m_MeshGroupMask, -1);
				cout << "[DEBUG] Set weapon MeshGroupMask to -1 (was: " << currentMeshGroupMask << ")" << endl;
			}
		}

		// 2. Aplicar configurações da skin usando offsets corretos do guia UC
		const auto attributeManager = memory.Read<uintptr_t>(weapon + offset::m_AttributeManager);
		if (attributeManager && attributeManager > 0x1000)
		{
			const auto item = memory.Read<uintptr_t>(attributeManager + offset::m_Item);
			if (item && item > 0x1000)
			{
				// Aplicar via AttributeManager -> Item
				memory.Write<int32_t>(item + offset::m_iItemIDHigh, -1);
				memory.Write<int32_t>(item + offset::m_iEntityQuality, 2);

				// Definir nome customizado (exemplo do guia UC)
				const char* customName = "Alani-NU";
				memory.WriteString(item + 0x2D0, customName);
			}
		}

		// 3. Aplicar fallback values diretamente na arma usando offsets corretos
		memory.Write<int32_t>(weapon + offset::m_nFallbackPaintKit, skinConfig.paintKit);
		memory.Write<int32_t>(weapon + offset::m_nFallbackSeed, skinConfig.seed);
		memory.Write<float>(weapon + offset::m_flFallbackWear, skinConfig.wear);
		memory.Write<int32_t>(weapon + offset::m_nFallbackStatTrak, skinConfig.statTrak);

		cout << "[DEBUG] Skin configuration applied successfully!" << endl;
		return true;
	}
	catch (...)
	{
		cout << "[ERROR] Exception occurred while applying skin" << endl;
		return false;
	}
}

int main()
{
	cout << "=== CS2 External Skin Changer ===" << endl;
	cout << "Desenvolvido com offsets atualizados para 2025-07-08" << endl;
	cout << "Pressione Enter para iniciar..." << endl;
	cin.get();

	cout << "[DEBUG] Inicializando memoria..." << endl;
	const auto memory = Memory("cs2.exe");

	cout << "[DEBUG] Obtendo enderecos dos modulos..." << endl;
	const auto client = memory.GetModuleAddress("client.dll");
	const auto engine = memory.GetModuleAddress("engine2.dll");

	if (!client || !engine)
	{
		cout << "[ERROR] Falha ao obter endereco dos modulos." << endl;
		cout << "Certifique-se de que o CS2 esta rodando." << endl;
		cin.get();
		return 0;
	}

	cout << "[DEBUG] Client base: 0x" << hex << client << endl;
	cout << "[DEBUG] Engine base: 0x" << hex << engine << endl;

	cout << "[INFO] Skin Changer iniciado! Pressione Ctrl+C para parar." << endl;

	while (true)
	{
		try
		{
			const auto localPlayer = memory.Read<uintptr_t>(client + offset::dwLocalPlayerPawn);
			if (!localPlayer)
			{
				cout << "[DEBUG] Local player not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Local player found: 0x" << hex << localPlayer << endl;

			const auto weaponServices = memory.Read<uintptr_t>(localPlayer + offset::m_pWeaponServices);
			if (!weaponServices)
			{
				cout << "[DEBUG] Weapon services not found, waiting..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			cout << "[DEBUG] Weapon services found: 0x" << hex << weaponServices << endl;

			// OPÇÃO 1: Processar apenas a arma ativa (recomendado)
			cout << "[DEBUG] === PROCESSANDO APENAS ARMA ATIVA ===" << endl;

			// Ler apenas a arma ativa usando m_hActiveWeapon
			const auto activeWeaponHandle = memory.Read<uint32_t>(weaponServices + offset::m_hActiveWeapon);
			cout << "[DEBUG] Active weapon handle: 0x" << hex << activeWeaponHandle << endl;

			if (!activeWeaponHandle)
			{
				cout << "[DEBUG] No active weapon, skipping..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			// Processar apenas a arma ativa
			array<uint32_t, 1> weaponHandles = { activeWeaponHandle };
			int weaponCount = 1;

			// OPÇÃO 2: Se quiser processar TODAS as armas (descomente as linhas abaixo)
			/*
			cout << "[DEBUG] === PROCESSANDO TODAS AS ARMAS ===" << endl;
			const auto weaponVectorBase = weaponServices + offset::m_hMyWeapons;
			const auto weaponCount = memory.Read<int>(weaponVectorBase + 0x0);
			const auto weaponsArray = memory.Read<uintptr_t>(weaponVectorBase + 0x8);

			cout << "[DEBUG] Weapon count: " << dec << weaponCount << endl;
			cout << "[DEBUG] Weapons array: 0x" << hex << weaponsArray << endl;

			if (!weaponsArray || weaponCount <= 0 || weaponCount > 64)
			{
				cout << "[DEBUG] Invalid weapons data, skipping..." << endl;
				this_thread::sleep_for(chrono::milliseconds(1000));
				continue;
			}

			array<uint32_t, 64> weaponHandles{};
			for (int i = 0; i < min(weaponCount, 64); i++)
			{
				weaponHandles[i] = memory.Read<uint32_t>(weaponsArray + (i * 0x4));
			}
			*/

			bool needsUpdate = false;
			for (int i = 0; i < weaponCount; i++)
			{
				const auto weaponHandle = weaponHandles[i];
				if (!weaponHandle) continue;

				cout << "[DEBUG] Processing ACTIVE weapon handle: 0x" << hex << weaponHandle << endl;

				// Usar método CHandle do guia UC
				const auto handle = weaponHandle & 0x7FFF;
				const auto listEntry = handle >> 9;
				const auto listIndex = handle & 0x1FF;

				const auto weaponListEntry = memory.Read<uintptr_t>(
					client + offset::dwEntityList + 8 * listEntry + 16);
				if (!weaponListEntry)
				{
					cout << "[DEBUG] Invalid weapon list entry for handle: 0x" << hex << weaponHandle << endl;
					continue;
				}

				const auto weapon = memory.Read<uintptr_t>(weaponListEntry + 120 * listIndex);
				if (!weapon)
				{
					cout << "[DEBUG] Invalid weapon entity for handle: 0x" << hex << weaponHandle << endl;
					continue;
				}

				cout << "[DEBUG] Weapon entity: 0x" << hex << weapon << endl;

				// Validar se o endereço da arma é válido
				if (weapon < 0x1000 || weapon > 0x7FFFFFFFFFFF)
				{
					cout << "[DEBUG] Invalid weapon address: 0x" << hex << weapon << endl;
					continue;
				}

				// PRIMEIRO: Identificar a arma pelo nome
				const auto weaponName = IdentifyWeaponByName(memory, localPlayer, weapon);
				cout << "[DEBUG] Identified weapon name: " << weaponName << endl;

				// Obter o ID correto baseado no nome
				const auto correctWeaponID = GetCorrectWeaponID(weaponName);
				cout << "[DEBUG] Correct weapon ID for '" << weaponName << "': " << dec << correctWeaponID << endl;

				// Se conseguimos identificar a arma pelo nome, usar o ID correto
				uint16_t itemDefIndex = 0;
				bool foundValidIndex = false;

				if (correctWeaponID > 0)
				{
					itemDefIndex = correctWeaponID;
					foundValidIndex = true;
					cout << "[DEBUG] Using weapon ID from name identification: " << dec << itemDefIndex << endl;
				}
				else
				{
					// Fallback: Tentar ler o ID pelos métodos antigos
					itemDefIndex = memory.Read<uint16_t>(weapon + offset::m_AttributeManager + offset::m_Item + offset::m_iItemDefinitionIndex);
					cout << "[DEBUG] Fallback method - Item definition index: " << dec << itemDefIndex << endl;

					if (itemDefIndex > 0 && itemDefIndex < 1000)
					{
						foundValidIndex = true;
						cout << "[DEBUG] Found valid weapon ID using fallback method" << endl;
					}
				}

				// Método 2: Fallback - tentar offsets alternativos se o método UC falhou
				if (!foundValidIndex)
				{
					cout << "[DEBUG] Method 2 - Trying alternative offsets..." << endl;

					// Lista expandida de IDs de armas conhecidas (incluindo IDs mais altos)
					std::array<uint16_t, 50> knownWeaponIds = {
						1, 2, 3, 4, 7, 8, 9, 10, 11, 13, 14, 16, 17, 19, 24, 25, 26, 27, 28, 29,
						30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 42, 59, 60, 61, 63, 64, 71, 132,
						322, 368, 376, 480, 736, 1675, 4111
					};

					// Tentar alguns offsets alternativos conhecidos
					std::array<std::ptrdiff_t, 3> alternativeOffsets = {
						0x1302,  // Offset alternativo comum
						0x11F2,  // Offset alternativo 1
						0x1352   // Offset alternativo 2
					};

					for (size_t i = 0; i < alternativeOffsets.size(); i++)
					{
						const auto altItemDefIndex = memory.Read<uint16_t>(weapon + alternativeOffsets[i]);
						cout << "[DEBUG] Method 2." << (i+1) << " - Alternative item definition index: " << dec << altItemDefIndex << endl;

						// Usar a mesma validação expandida
						bool isKnownWeapon = false;
						for (auto knownId : knownWeaponIds)
						{
							if (altItemDefIndex == knownId)
							{
								isKnownWeapon = true;
								break;
							}
						}

						if (altItemDefIndex > 0 && (altItemDefIndex < 1000 || isKnownWeapon))
						{
							itemDefIndex = altItemDefIndex;
							foundValidIndex = true;
							cout << "[DEBUG] Found valid ID using alternative offset 0x" << hex << alternativeOffsets[i] << endl;
							break;
						}
					}
				}
				// Processar apenas se encontramos um índice válido
				if (foundValidIndex)
				{
					// Usar nome baseado no ID para maior confiabilidade
					const auto weaponName = GetWeaponNameFromID(itemDefIndex);
					cout << "[DEBUG] Valid weapon found - ID: " << dec << itemDefIndex
						 << ", Name: " << weaponName << endl;

					if (ApplySkinToWeapon(memory, weapon, itemDefIndex))
					{
						needsUpdate = true;
						cout << "[DEBUG] Skin applied successfully to " << weaponName
							 << " (ID: " << itemDefIndex << ")" << endl;
					}
				}
				else
				{
					cout << "[DEBUG] No valid item definition index found for weapon: "
						 << weaponName << " (0x" << hex << weapon << ")" << endl;
				}
			}

			// Forcar atualizacao se necessario
			if (needsUpdate)
			{
				// Aplicar também no ViewModel (primeira pessoa) conforme guia UC
				cout << "[DEBUG] Applying ViewModel updates..." << endl;
				ForceViewModelUpdate(memory, client, localPlayer);

				// Aplicar o offset adicional mencionado no guia UC
				const auto weaponModelPtr = memory.Read<uintptr_t>(client + 0x180EC58);
				if (weaponModelPtr)
				{
					memory.Write<int32_t>(weaponModelPtr + 0x10, 2);
					cout << "[DEBUG] Set weapon model pointer MeshGroupMask to 2" << endl;
				}

				ForceFullUpdate(memory, engine);
			}

			this_thread::sleep_for(chrono::milliseconds(500));
		}
		catch (const exception& e)
		{
			cout << "[ERROR] Excecao capturada: " << e.what() << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
		}
	}

	return 0;
}
