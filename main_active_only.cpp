#include "memory.h"
#include "offsets.h"
#include <thread>
#include <iostream>

using namespace std;

// Função para obter paint kit baseado no item definition index
constexpr const int GetWeaponPaint(const short& itemdefinition)
{
	switch (itemdefinition)
	{
		case 7: return 433;  // AK-47
		case 9: return 344;  // AWP
		case 4: return 437;  // Glock
		case 61: return 504; // USP
		case 17: return 188; // MAC-10
		case 11: return 128; // G3SG1
		case 16: return 309; // M4A4
		case 1: return 156;  // Deagle
		case 2: return 156;  // Dual Berettas
		default: return 0;
	}
}

int main()
{
	cout << "=== CS2 External Skin Changer (ACTIVE WEAPON ONLY) ===" << endl;
	cout << "Press enter to start..." << endl;
	cin.get();

	const auto memory = Memory("cs2.exe");
	const auto client = memory.GetModuleAddress("client.dll");
	const auto engine = memory.GetModuleAddress("engine2.dll");

	if (!client || !engine)
	{
		cout << "[ERROR] Failed to get module address." << endl;
		return 0;
	}

	cout << "[DEBUG] Client base: 0x" << hex << client << endl;
	cout << "[DEBUG] Engine base: 0x" << hex << engine << endl;

	uint32_t lastActiveWeaponHandle = 0;

	while (true)
	{
		const auto localPlayer = memory.Read<uintptr_t>(client + offset::dwLocalPlayerPawn);
		if (!localPlayer)
		{
			this_thread::sleep_for(chrono::milliseconds(1000));
			continue;
		}

		const auto m_pWeaponServices = memory.Read<uintptr_t>(localPlayer + offset::m_pWeaponServices);
		if (!m_pWeaponServices)
		{
			this_thread::sleep_for(chrono::milliseconds(1000));
			continue;
		}

		// LER APENAS A ARMA ATIVA
		const auto activeWeaponHandle = memory.Read<uint32_t>(m_pWeaponServices + offset::m_hActiveWeapon);
		if (!activeWeaponHandle)
		{
			this_thread::sleep_for(chrono::milliseconds(100));
			continue;
		}

		// DETECTAR MUDANÇA DE ARMA
		if (activeWeaponHandle == lastActiveWeaponHandle)
		{
			this_thread::sleep_for(chrono::milliseconds(100));
			continue;
		}

		lastActiveWeaponHandle = activeWeaponHandle;
		cout << "[DEBUG] *** NEW ACTIVE WEAPON DETECTED *** Handle: 0x" << hex << activeWeaponHandle << endl;

		// CONVERTER HANDLE PARA ENTIDADE
		const auto weaponListEntry = memory.Read<uintptr_t>(
			client + offset::dwEntityList + (0x8 * ((activeWeaponHandle & 0x7FFF) >> 9)) + 16);
		if (!weaponListEntry)
		{
			cout << "[WARNING] Invalid weapon list entry." << endl;
			continue;
		}

		const auto weapon = memory.Read<uintptr_t>(weaponListEntry + 120 * (activeWeaponHandle & 0x1FF));
		if (!weapon)
		{
			cout << "[WARNING] Invalid weapon entity." << endl;
			continue;
		}

		// VALIDAÇÃO RIGOROSA DO ENDEREÇO DA ARMA
		if (weapon < 0x10000 || weapon > 0x7FFFFFFFFFFF)
		{
			cout << "[WARNING] Invalid weapon address: 0x" << hex << weapon << " - SKIPPING TO AVOID CRASH" << endl;
			continue;
		}

		// VERIFICAR SE O ENDEREÇO PARECE SER TEXTO (ASCII)
		if ((weapon & 0xFF) >= 0x20 && (weapon & 0xFF) <= 0x7E)
		{
			cout << "[WARNING] Weapon address looks like ASCII text: 0x" << hex << weapon << " - SKIPPING" << endl;
			continue;
		}

		cout << "[DEBUG] Active Weapon Entity: 0x" << hex << weapon << " (VALIDATED)" << endl;

		// TENTAR LER ITEM DEFINITION INDEX
		short itemDefIndex = memory.Read<short>(weapon + offset::m_iItemDefinitionIndex);
		cout << "[DEBUG] Item Definition Index: " << dec << itemDefIndex << endl;

		// SE NÃO CONSEGUIU LER, USAR FALLBACK
		if (itemDefIndex <= 0 || itemDefIndex >= 200)
		{
			itemDefIndex = 7; // AK-47 como padrão
			cout << "[DEBUG] Using fallback ID: " << dec << itemDefIndex << endl;
		}

		if (const auto paint = GetWeaponPaint(itemDefIndex))
		{
			cout << "[DEBUG] *** APPLYING SKIN TO ACTIVE WEAPON *** ID: " << itemDefIndex << ", Paint: " << paint << endl;

			// VALIDAR ANTES DE LER/ESCREVER
			int32_t currentPaintKit = 0;
			try
			{
				currentPaintKit = memory.Read<int32_t>(weapon + offset::m_nFallbackPaintKit);
			}
			catch (...)
			{
				cout << "[ERROR] Failed to read current paint kit - UNSAFE WEAPON ADDRESS" << endl;
				continue;
			}

			bool needsUpdate = currentPaintKit != paint;
			if (needsUpdate)
			{
				cout << "[DEBUG] Applying skin safely..." << endl;

				try
				{
					// APLICAR SKIN COM VALIDAÇÃO
					memory.Write<int32_t>(weapon + offset::m_iItemIDHigh, -1);
					memory.Write<int32_t>(weapon + offset::m_nFallbackPaintKit, paint);
					memory.Write<float>(weapon + offset::m_flFallbackWear, 0.1f);
					memory.Write<int32_t>(weapon + offset::m_nFallbackStatTrak, 6969);

					// LER ORIGINAL OWNER SAFELY
					int32_t originalOwner = 0;
					try {
						originalOwner = memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow);
					} catch (...) {
						originalOwner = 0; // Fallback
					}
					memory.Write<int32_t>(weapon + offset::m_iAccountID, originalOwner);

					cout << "[DEBUG] *** SKIN APPLIED SAFELY TO ACTIVE WEAPON ***" << endl;
				}
				catch (...)
				{
					cout << "[ERROR] Failed to apply skin - MEMORY WRITE ERROR" << endl;
					continue;
				}

				// FORCE UPDATE SEGURO
				try
				{
					const auto networkClient = memory.Read<uintptr_t>(engine + offset::dwNetworkGameClient);
					if (networkClient > 0x10000 && networkClient < 0x7FFFFFFFFFFF)
					{
						memory.Write<int32_t>(networkClient + offset::dwNetworkGameClient_deltaTick, -1);
						cout << "[DEBUG] Forced update applied safely." << endl;
					}
					else
					{
						cout << "[WARNING] Invalid network client address: 0x" << hex << networkClient << endl;
					}
				}
				catch (...)
				{
					cout << "[ERROR] Failed to apply force update" << endl;
				}

				// AGUARDAR MAIS TEMPO PARA PROCESSAR
				this_thread::sleep_for(chrono::milliseconds(2000));
			}
			else
			{
				cout << "[DEBUG] Skin already applied to active weapon." << endl;
			}
		}

		this_thread::sleep_for(chrono::milliseconds(500));
	}

	return 0;
}
