#include "memory.h"
#include "offsets.h"
#include "skins.h"
#include <thread>
#include <iostream>
#include <array>

using namespace std;

// Função para obter paint kit baseado no item definition index (como no exemplo funcional)
constexpr const int GetWeaponPaint(const short& itemdefinition)
{
	switch (itemdefinition)
	{
		case 7: return 433;  // AK-47
		case 9: return 344;  // AWP
		case 4: return 437;  // Glock
		case 61: return 504; // USP
		case 17: return 188; // MAC-10
		case 11: return 128; // G3SG1
		case 16: return 309; // M4A4
		case 1: return 156;  // Deagle
		case 2: return 156;  // Dual Berettas
		default: return 0;
	}
}

int main()
{
	cout << "=== CS2 External Skin Changer (Functional Example Logic) ===" << endl;
	cout << "Press enter to start..." << endl;
	cin.get();

	cout << "[DEBUG] Initializing memory..." << endl;
	const auto memory = Memory("cs2.exe");

	cout << "[DEBUG] Fetching module addresses..." << endl;
	const auto client = memory.GetModuleAddress("client.dll");
	const auto engine = memory.GetModuleAddress("engine2.dll");

	if (!client || !engine)
	{
		cout << "[ERROR] Failed to get module address." << endl;
		return 0;
	}

	cout << "[DEBUG] Client base: 0x" << hex << client << endl;
	cout << "[DEBUG] Engine base: 0x" << hex << engine << endl;

	while (true)
	{
		const auto localPlayer = memory.Read<uintptr_t>(client + offset::dwLocalPlayerPawn);
		if (!localPlayer)
		{
			cout << "[ERROR] Failed to read local player address." << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
			continue;
		}
		cout << "[DEBUG] Local player: 0x" << hex << localPlayer << endl;

		const auto m_pWeaponServices = memory.Read<uintptr_t>(localPlayer + offset::m_pWeaponServices);
		if (!m_pWeaponServices)
		{
			cout << "[ERROR] Failed to read Weapon Services." << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
			continue;
		}
		cout << "[DEBUG] Weapon Services: 0x" << hex << m_pWeaponServices << endl;

		// USAR ESTRUTURA DE VETOR CORRETA (como no nosso código anterior)
		const auto weaponVectorBase = m_pWeaponServices + offset::m_hMyWeapons;
		const auto weaponCount = memory.Read<int>(weaponVectorBase + 0x0);
		const auto weaponsArray = memory.Read<uintptr_t>(weaponVectorBase + 0x8);

		cout << "[DEBUG] Weapon Count: " << weaponCount << endl;
		cout << "[DEBUG] Weapons Array: 0x" << hex << weaponsArray << endl;

		if (!weaponsArray || weaponCount <= 0 || weaponCount > 64)
		{
			cout << "[DEBUG] Invalid weapons data, skipping..." << endl;
			this_thread::sleep_for(chrono::milliseconds(1000));
			continue;
		}

		array<uintptr_t, 8> weapons{};
		for (int i = 0; i < min(weaponCount, 8); i++)
		{
			weapons[i] = memory.Read<uint32_t>(weaponsArray + (i * 0x4));
		}

		bool update = false;
		for (const auto& handle : weapons)
		{
			if (!handle)
				continue;

			const auto weaponListEntry = memory.Read<uintptr_t>(
				client + offset::dwEntityList + (0x8 * ((handle & 0x7FFF) >> 9)) + 16);
			if (!weaponListEntry)
			{
				cout << "[WARNING] Invalid weapon list entry." << endl;
				continue;
			}

			const auto weapon = memory.Read<uintptr_t>(weaponListEntry + 120 * (handle & 0x1FF));
			if (!weapon)
			{
				cout << "[WARNING] Invalid weapon entity." << endl;
				continue;
			}

			cout << "[DEBUG] Weapon Entity: 0x" << hex << weapon << endl;

			// TENTAR MÚLTIPLOS OFFSETS PARA ITEM DEFINITION INDEX
			short itemDefIndex = 0;
			std::array<std::ptrdiff_t, 5> possibleOffsets = {
				0x1BA,  // Offset atual
				0x1B2,  // Offset alternativo 1
				0x1C0,  // Offset alternativo 2
				0x1A8,  // Offset alternativo 3
				0x1D0   // Offset alternativo 4
			};

			for (auto testOffset : possibleOffsets)
			{
				short testID = memory.Read<short>(weapon + testOffset);
				if (testID > 0 && testID < 200)
				{
					itemDefIndex = testID;
					cout << "[DEBUG] Found valid ID " << dec << itemDefIndex << " at offset 0x" << hex << testOffset << endl;
					break;
				}
			}

			cout << "[DEBUG] Final Item Definition Index: " << dec << itemDefIndex << endl;

			// SE NÃO ENCONTROU ID VÁLIDO, USAR ID BASEADO NO ENDEREÇO DA ARMA
			if (itemDefIndex <= 0 || itemDefIndex >= 200)
			{
				// Usar endereço da arma para determinar ID
				static int weaponCounter = 0;
				std::array<int, 8> fallbackIDs = {7, 9, 4, 61, 17, 11, 16, 1};
				itemDefIndex = fallbackIDs[weaponCounter % fallbackIDs.size()];
				weaponCounter++;
				cout << "[DEBUG] Using fallback ID: " << dec << itemDefIndex << endl;
			}

			if (const auto paint = GetWeaponPaint(itemDefIndex))
			{
				cout << "[DEBUG] Applying skin: " << paint << " to weapon ID: " << itemDefIndex << endl;

				bool check = memory.Read<int32_t>(weapon + offset::m_nFallbackPaintKit) != paint;
				if (check)
				{
					update = true;
				}

				// APLICAR EXATAMENTE COMO NO EXEMPLO FUNCIONAL
				memory.Write<int32_t>(weapon + offset::m_iItemIDHigh, -1);
				memory.Write<int32_t>(weapon + offset::m_nFallbackPaintKit, paint);
				memory.Write<float>(weapon + offset::m_flFallbackWear, 0.1f);
				memory.Write<int32_t>(weapon + offset::m_nFallbackStatTrak, 6969);
				memory.Write<int32_t>(weapon + offset::m_iAccountID,
					memory.Read<int32_t>(weapon + offset::m_OriginalOwnerXuidLow));

				cout << "[DEBUG] *** SKIN APPLIED SUCCESSFULLY *** to weapon ID " << itemDefIndex << "!" << endl;
			}
		}

		// ViewModel update (como no exemplo funcional)
		const auto m_pViewModelServices = memory.Read<uintptr_t>(localPlayer + offset::m_pViewModelServices);
		if (m_pViewModelServices)
		{
			const auto ViewHandle = memory.Read<uintptr_t>(m_pViewModelServices + offset::m_hViewModel);
			if (ViewHandle)
			{
				const auto ViewListEntry = memory.Read<uintptr_t>(
					client + offset::dwEntityList + (0x8 * ((ViewHandle & 0x7FFF) >> 9)) + 16);
				if (ViewListEntry)
				{
					const auto ViewController = memory.Read<uintptr_t>(ViewListEntry + 120 * (ViewHandle & 0x1FF));
					if (ViewController)
					{
						const auto ViewNode = memory.Read<uintptr_t>(ViewController + 0x318);
						const auto ViewMask = memory.Read<uint64_t>(ViewNode + 0x160);
						if (ViewMask != 2)
						{
							memory.Write<uint64_t>(ViewNode + 0x160, 2);
							cout << "[DEBUG] Forced ViewModel update." << endl;
						}
					}
				}
			}
		}

		// Force update (como no exemplo funcional)
		if (update)
		{
			memory.Write<int32_t>(memory.Read<uintptr_t>(engine + offset::dwNetworkGameClient) + offset::dwNetworkGameClient_deltaTick, -1);
			cout << "[DEBUG] Forced update by setting delta tick to -1." << endl;
			update = false;
		}

		this_thread::sleep_for(chrono::milliseconds(100));
	}

	return 0;
}
