#pragma once
#include <cstddef>

// Offsets atualizados para CS2 - 2025-07-08 (baseados nos dumps corretos)
namespace offset
{
	// Client.dll offsets (do output/offsets.hpp)
	constexpr ::std::ptrdiff_t dwLocalPlayerPawn = 0x18580D0;
	constexpr ::std::ptrdiff_t dwEntityList = 0x1A044E0;
	constexpr ::std::ptrdiff_t dwViewMatrix = 0x1A6D280;
	constexpr ::std::ptrdiff_t dwLocalPlayerController = 0x1A52D20;

	// Engine2.dll offsets
	constexpr ::std::ptrdiff_t dwNetworkGameClient = 0x53FCE0;
	constexpr ::std::ptrdiff_t dwNetworkGameClient_deltaTick = 0x27C;

	// Player offsets (do output/client_dll.hpp - C_BasePlayerPawn)
	constexpr ::std::ptrdiff_t m_pWeaponServices = 0x11A8;
	constexpr ::std::ptrdiff_t m_pViewModelServices = 0x1368;

	// Weapon Services offsets (do output/client_dll.hpp - CPlayer_WeaponServices)
	constexpr ::std::ptrdiff_t m_hMyWeapons = 0x40;

	// ViewModel Services offsets
	constexpr ::std::ptrdiff_t m_hViewModel = 0x40;

	// Player specific offsets (do output/client_dll.hpp - C_CSPlayerPawn)
	constexpr ::std::ptrdiff_t m_pClippingWeapon = 0x13A0; // C_CSWeaponBase*

	// Weapon/Item offsets (do output/client_dll.hpp)
	// C_CSWeaponBase herda de C_EconEntity
	constexpr ::std::ptrdiff_t m_AttributeManager = 0x1148; // C_EconEntity -> C_AttributeContainer
	constexpr ::std::ptrdiff_t m_Item = 0x50;               // C_AttributeContainer -> C_EconItemView
	constexpr ::std::ptrdiff_t m_iItemDefinitionIndex = 0x1BA; // C_EconItemView
	constexpr ::std::ptrdiff_t m_iEntityQuality = 0x1BC;    // C_EconItemView
	constexpr ::std::ptrdiff_t m_iItemIDHigh = 0x1D0;       // C_EconItemView
	constexpr ::std::ptrdiff_t m_iAccountID = 0x1D8;        // C_EconItemView

	// Fallback offsets (C_EconEntity)
	constexpr ::std::ptrdiff_t m_OriginalOwnerXuidLow = 0x15F0;
	constexpr ::std::ptrdiff_t m_nFallbackPaintKit = 0x15F8;
	constexpr ::std::ptrdiff_t m_nFallbackSeed = 0x15FC;
	constexpr ::std::ptrdiff_t m_flFallbackWear = 0x1600;
	constexpr ::std::ptrdiff_t m_nFallbackStatTrak = 0x1604;
}
