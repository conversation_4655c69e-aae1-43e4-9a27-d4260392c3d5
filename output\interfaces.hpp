// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-08 17:57:02.507072200 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace interfaces {
        // Module: animationsystem.dll
        namespace animationsystem_dll {
            constexpr std::ptrdiff_t AnimationSystemUtils_001 = 0x60FE28;
            constexpr std::ptrdiff_t AnimationSystem_001 = 0x607D50;
        }
        // Module: client.dll
        namespace client_dll {
            constexpr std::ptrdiff_t ClientToolsInfo_001 = 0x184FED0;
            constexpr std::ptrdiff_t EmptyWorldService001_Client = 0x180C0F0;
            constexpr std::ptrdiff_t GameClientExports001 = 0x184CB88;
            constexpr std::ptrdiff_t LegacyGameUI001 = 0x186D1F0;
            constexpr std::ptrdiff_t Source2Client002 = 0x1A668A0;
            constexpr std::ptrdiff_t Source2ClientConfig001 = 0x19FF490;
            constexpr std::ptrdiff_t Source2ClientPrediction001 = 0x1857F50;
            constexpr std::ptrdiff_t Source2ClientUI001 = 0x186B720;
        }
        // Module: engine2.dll
        namespace engine2_dll {
            constexpr std::ptrdiff_t BenchmarkService001 = 0x546D30;
            constexpr std::ptrdiff_t BugService001 = 0x5DFD10;
            constexpr std::ptrdiff_t ClientServerEngineLoopService_001 = 0x547A70;
            constexpr std::ptrdiff_t EngineGameUI001 = 0x544B90;
            constexpr std::ptrdiff_t EngineServiceMgr001 = 0x620BF0;
            constexpr std::ptrdiff_t GameEventSystemClientV001 = 0x620F10;
            constexpr std::ptrdiff_t GameEventSystemServerV001 = 0x621050;
            constexpr std::ptrdiff_t GameResourceServiceClientV001 = 0x546E30;
            constexpr std::ptrdiff_t GameResourceServiceServerV001 = 0x546E90;
            constexpr std::ptrdiff_t GameUIService_001 = 0x5E0010;
            constexpr std::ptrdiff_t HostStateMgr001 = 0x547960;
            constexpr std::ptrdiff_t INETSUPPORT_001 = 0x540060;
            constexpr std::ptrdiff_t InputService_001 = 0x5E0320;
            constexpr std::ptrdiff_t KeyValueCache001 = 0x547A10;
            constexpr std::ptrdiff_t MapListService_001 = 0x61F370;
            constexpr std::ptrdiff_t NetworkClientService_001 = 0x61F500;
            constexpr std::ptrdiff_t NetworkP2PService_001 = 0x547140;
            constexpr std::ptrdiff_t NetworkServerService_001 = 0x61F890;
            constexpr std::ptrdiff_t NetworkService_001 = 0x547290;
            constexpr std::ptrdiff_t RenderService_001 = 0x61FAF0;
            constexpr std::ptrdiff_t ScreenshotService001 = 0x61FD90;
            constexpr std::ptrdiff_t SimpleEngineLoopService_001 = 0x547B80;
            constexpr std::ptrdiff_t SoundService_001 = 0x5472D0;
            constexpr std::ptrdiff_t Source2EngineToClient001 = 0x544210;
            constexpr std::ptrdiff_t Source2EngineToClientStringTable001 = 0x544270;
            constexpr std::ptrdiff_t Source2EngineToServer001 = 0x544308;
            constexpr std::ptrdiff_t Source2EngineToServerStringTable001 = 0x544330;
            constexpr std::ptrdiff_t SplitScreenService_001 = 0x5475D0;
            constexpr std::ptrdiff_t StatsService_001 = 0x6201C0;
            constexpr std::ptrdiff_t ToolService_001 = 0x547790;
            constexpr std::ptrdiff_t VENGINE_GAMEUIFUNCS_VERSION005 = 0x544C20;
            constexpr std::ptrdiff_t VProfService_001 = 0x5477D0;
        }
        // Module: filesystem_stdio.dll
        namespace filesystem_stdio_dll {
            constexpr std::ptrdiff_t VAsyncFileSystem2_001 = 0x20C590;
            constexpr std::ptrdiff_t VFileSystem017 = 0x211840;
        }
        // Module: host.dll
        namespace host_dll {
            constexpr std::ptrdiff_t DebugDrawQueueManager001 = 0x136FE0;
            constexpr std::ptrdiff_t GameModelInfo001 = 0x137020;
            constexpr std::ptrdiff_t GameSystem2HostHook = 0x137060;
            constexpr std::ptrdiff_t HostUtils001 = 0x137090;
            constexpr std::ptrdiff_t PredictionDiffManager001 = 0x1372E0;
            constexpr std::ptrdiff_t SaveRestoreDataVersion001 = 0x137410;
            constexpr std::ptrdiff_t SinglePlayerSharedMemory001 = 0x137440;
            constexpr std::ptrdiff_t Source2Host001 = 0x1374B0;
        }
        // Module: imemanager.dll
        namespace imemanager_dll {
            constexpr std::ptrdiff_t IMEManager001 = 0x2EA50;
        }
        // Module: inputsystem.dll
        namespace inputsystem_dll {
            constexpr std::ptrdiff_t InputStackSystemVersion001 = 0x36B70;
            constexpr std::ptrdiff_t InputSystemVersion001 = 0x387E0;
        }
        // Module: localize.dll
        namespace localize_dll {
            constexpr std::ptrdiff_t Localize_001 = 0x3AAD0;
        }
        // Module: matchmaking.dll
        namespace matchmaking_dll {
            constexpr std::ptrdiff_t GameTypes001 = 0x1A52E0;
            constexpr std::ptrdiff_t MATCHFRAMEWORK_001 = 0x1AD510;
        }
        // Module: materialsystem2.dll
        namespace materialsystem2_dll {
            constexpr std::ptrdiff_t FontManager_001 = 0x114330;
            constexpr std::ptrdiff_t MaterialUtils_001 = 0x10F4C0;
            constexpr std::ptrdiff_t PostProcessingSystem_001 = 0x10F3D0;
            constexpr std::ptrdiff_t TextLayout_001 = 0x10F450;
            constexpr std::ptrdiff_t VMaterialSystem2_001 = 0x113910;
        }
        // Module: meshsystem.dll
        namespace meshsystem_dll {
            constexpr std::ptrdiff_t MeshSystem001 = 0x19D610;
        }
        // Module: navsystem.dll
        namespace navsystem_dll {
            constexpr std::ptrdiff_t NavSystem001 = 0xFB730;
        }
        // Module: networksystem.dll
        namespace networksystem_dll {
            constexpr std::ptrdiff_t FlattenedSerializersVersion001 = 0x244570;
            constexpr std::ptrdiff_t NetworkMessagesVersion001 = 0x2765E0;
            constexpr std::ptrdiff_t NetworkSystemVersion001 = 0x26E300;
            constexpr std::ptrdiff_t SerializedEntitiesVersion001 = 0x26E3F0;
        }
        // Module: panorama.dll
        namespace panorama_dll {
            constexpr std::ptrdiff_t PanoramaUIEngine001 = 0x4E9250;
        }
        // Module: panorama_text_pango.dll
        namespace panorama_text_pango_dll {
            constexpr std::ptrdiff_t PanoramaTextServices001 = 0x2B38E0;
        }
        // Module: panoramauiclient.dll
        namespace panoramauiclient_dll {
            constexpr std::ptrdiff_t PanoramaUIClient001 = 0x28D840;
        }
        // Module: particles.dll
        namespace particles_dll {
            constexpr std::ptrdiff_t ParticleSystemMgr003 = 0x629C30;
        }
        // Module: pulse_system.dll
        namespace pulse_system_dll {
            constexpr std::ptrdiff_t IPulseSystem_001 = 0x17D9A0;
        }
        // Module: rendersystemdx11.dll
        namespace rendersystemdx11_dll {
            constexpr std::ptrdiff_t RenderDeviceMgr001 = 0x3EE1F0;
            constexpr std::ptrdiff_t RenderUtils_001 = 0x3EEA58;
            constexpr std::ptrdiff_t VRenderDeviceMgrBackdoor001 = 0x3EE290;
        }
        // Module: resourcesystem.dll
        namespace resourcesystem_dll {
            constexpr std::ptrdiff_t ResourceSystem013 = 0x72A40;
        }
        // Module: scenefilecache.dll
        namespace scenefilecache_dll {
            constexpr std::ptrdiff_t ResponseRulesCache001 = 0x720F0;
            constexpr std::ptrdiff_t SceneFileCache002 = 0x72260;
        }
        // Module: scenesystem.dll
        namespace scenesystem_dll {
            constexpr std::ptrdiff_t RenderingPipelines_001 = 0x5CEB30;
            constexpr std::ptrdiff_t SceneSystem_002 = 0x7AE000;
            constexpr std::ptrdiff_t SceneUtils_001 = 0x5CF380;
        }
        // Module: schemasystem.dll
        namespace schemasystem_dll {
            constexpr std::ptrdiff_t SchemaSystem_001 = 0x616E0;
        }
        // Module: server.dll
        namespace server_dll {
            constexpr std::ptrdiff_t EmptyWorldService001_Server = 0x136FB70;
            constexpr std::ptrdiff_t EntitySubclassUtilsV001 = 0x1320190;
            constexpr std::ptrdiff_t NavGameTest001 = 0x140EB98;
            constexpr std::ptrdiff_t ServerToolsInfo_001 = 0x13C4538;
            constexpr std::ptrdiff_t Source2GameClients001 = 0x13BE400;
            constexpr std::ptrdiff_t Source2GameDirector001 = 0x14F2160;
            constexpr std::ptrdiff_t Source2GameEntities001 = 0x13C4460;
            constexpr std::ptrdiff_t Source2Server001 = 0x13C42D0;
            constexpr std::ptrdiff_t Source2ServerConfig001 = 0x15BCBE8;
            constexpr std::ptrdiff_t customnavsystem001 = 0x1304908;
        }
        // Module: soundsystem.dll
        namespace soundsystem_dll {
            constexpr std::ptrdiff_t SoundOpSystem001 = 0x3A1C50;
            constexpr std::ptrdiff_t SoundOpSystemEdit001 = 0x3A1B20;
            constexpr std::ptrdiff_t SoundSystem001 = 0x3A15F0;
            constexpr std::ptrdiff_t VMixEditTool001 = 0x48289D0A;
        }
        // Module: steamaudio.dll
        namespace steamaudio_dll {
            constexpr std::ptrdiff_t SteamAudio001 = 0x2139F0;
        }
        // Module: steamclient64.dll
        namespace steamclient64_dll {
            constexpr std::ptrdiff_t CLIENTENGINE_INTERFACE_VERSION005 = 0xFFFFFFFF8BB0BEDA;
            constexpr std::ptrdiff_t IVALIDATE001 = 0x15348D8;
            constexpr std::ptrdiff_t SteamClient006 = 0x1532000;
            constexpr std::ptrdiff_t SteamClient007 = 0x1532008;
            constexpr std::ptrdiff_t SteamClient008 = 0x1532010;
            constexpr std::ptrdiff_t SteamClient009 = 0x1532018;
            constexpr std::ptrdiff_t SteamClient010 = 0x1532020;
            constexpr std::ptrdiff_t SteamClient011 = 0x1532028;
            constexpr std::ptrdiff_t SteamClient012 = 0x1532030;
            constexpr std::ptrdiff_t SteamClient013 = 0x1532038;
            constexpr std::ptrdiff_t SteamClient014 = 0x1532040;
            constexpr std::ptrdiff_t SteamClient015 = 0x1532048;
            constexpr std::ptrdiff_t SteamClient016 = 0x1532050;
            constexpr std::ptrdiff_t SteamClient017 = 0x1532058;
            constexpr std::ptrdiff_t SteamClient018 = 0x1532060;
            constexpr std::ptrdiff_t SteamClient019 = 0x1532068;
            constexpr std::ptrdiff_t SteamClient020 = 0x1532070;
            constexpr std::ptrdiff_t SteamClient021 = 0x1532078;
            constexpr std::ptrdiff_t SteamClient022 = 0x1532080;
            constexpr std::ptrdiff_t p2pvoice002 = 0x14E2D2F;
            constexpr std::ptrdiff_t p2pvoicesingleton002 = 0x150F0F0;
        }
        // Module: tier0.dll
        namespace tier0_dll {
            constexpr std::ptrdiff_t TestScriptMgr001 = 0x37EA80;
            constexpr std::ptrdiff_t VEngineCvar007 = 0x38D4E0;
            constexpr std::ptrdiff_t VProcessUtils002 = 0x37E990;
            constexpr std::ptrdiff_t VStringTokenSystem001 = 0x3A5F00;
        }
        // Module: v8system.dll
        namespace v8system_dll {
            constexpr std::ptrdiff_t Source2V8System001 = 0x2C480;
        }
        // Module: vphysics2.dll
        namespace vphysics2_dll {
            constexpr std::ptrdiff_t VPhysics2_Handle_Interface_001 = 0x391F70;
            constexpr std::ptrdiff_t VPhysics2_Interface_001 = 0x391FB0;
        }
        // Module: vscript.dll
        namespace vscript_dll {
            constexpr std::ptrdiff_t VScriptManager010 = 0x128600;
        }
        // Module: vstdlib_s64.dll
        namespace vstdlib_s64_dll {
            constexpr std::ptrdiff_t IVALIDATE001 = 0x6E990;
            constexpr std::ptrdiff_t VEngineCvar002 = 0x6D070;
        }
        // Module: worldrenderer.dll
        namespace worldrenderer_dll {
            constexpr std::ptrdiff_t WorldRendererMgr001 = 0x161D80;
        }
    }
}
