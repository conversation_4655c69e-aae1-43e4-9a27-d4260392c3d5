#pragma once
#include "weapon_ids.h"

// Definições de skins para diferentes armas
// Baseado nos IDs de definição de item do CS2

struct SkinConfig {
    int paintKit;
    float wear;
    int statTrak;
    int seed;
};

// Função para obter configuração de skin baseada no item definition index
constexpr SkinConfig GetWeaponSkin(const short& itemDefinitionIndex)
{
    switch (itemDefinitionIndex)
    {
        // Desert Eagle (ID: 1)
        case 1: 
            return { 37, 0.1f, 6969, 1 }; // Blaze
        
        // Dual Berettas (ID: 2)
        case 2: 
            return { 156, 0.1f, 6969, 1 }; // Hemoglobin
        
        // Five-SeveN (ID: 3)
        case 3: 
            return { 143, 0.1f, 6969, 1 }; // Case Hardened
        
        // Glock-18 (ID: 4)
        case 4: 
            return { 437, 0.1f, 6969, 1 }; // Water Elemental
        
        // AK-47 (ID: 7)
        case 7: 
            return { 433, 0.1f, 6969, 1 }; // Redline
        
        // AUG (ID: 8)
        case 8: 
            return { 455, 0.1f, 6969, 1 }; // Akihabara Accept
        
        // AWP (ID: 9)
        case 9: 
            return { 344, 0.1f, 6969, 1 }; // Dragon Lore
        
        // FAMAS (ID: 10)
        case 10: 
            return { 155, 0.1f, 6969, 1 }; // Afterimage
        
        // G3SG1 (ID: 11)
        case 11: 
            return { 128, 0.1f, 6969, 1 }; // Chronos
        
        // Galil AR (ID: 13)
        case 13: 
            return { 198, 0.1f, 6969, 1 }; // Chatterbox
        
        // M249 (ID: 14)
        case 14: 
            return { 131, 0.1f, 6969, 1 }; // System Lock
        
        // M4A4 (ID: 16)
        case 16: 
            return { 309, 0.1f, 6969, 1 }; // Howl
        
        // MAC-10 (ID: 17)
        case 17: 
            return { 188, 0.1f, 6969, 1 }; // Neon Rider
        
        // P90 (ID: 19)
        case 19: 
            return { 159, 0.1f, 6969, 1 }; // Asiimov
        
        // UMP-45 (ID: 24)
        case 24: 
            return { 286, 0.1f, 6969, 1 }; // Primal Saber
        
        // XM1014 (ID: 25)
        case 25: 
            return { 93, 0.1f, 6969, 1 }; // Tranquility
        
        // PP-Bizon (ID: 26)
        case 26: 
            return { 190, 0.1f, 6969, 1 }; // Judgement of Anubis
        
        // MAG-7 (ID: 27)
        case 27: 
            return { 165, 0.1f, 6969, 1 }; // Bulldozer
        
        // Negev (ID: 28)
        case 28: 
            return { 190, 0.1f, 6969, 1 }; // Loudmouth
        
        // Sawed-Off (ID: 29)
        case 29: 
            return { 168, 0.1f, 6969, 1 }; // The Kraken
        
        // Tec-9 (ID: 30)
        case 30: 
            return { 274, 0.1f, 6969, 1 }; // Fuel Injector
        
        // Zeus x27 (ID: 31)
        case 31: 
            return { 519, 0.1f, 6969, 1 }; // Olympus
        
        // HK P2000 (ID: 32)
        case 32: 
            return { 125, 0.1f, 6969, 1 }; // Asiimov
        
        // MP7 (ID: 33)
        case 33: 
            return { 102, 0.1f, 6969, 1 }; // Nemesis
        
        // MP9 (ID: 34)
        case 34: 
            return { 177, 0.1f, 6969, 1 }; // Hypnotic
        
        // Nova (ID: 35)
        case 35: 
            return { 176, 0.1f, 6969, 1 }; // Antique
        
        // P250 (ID: 36)
        case 36: 
            return { 125, 0.1f, 6969, 1 }; // Asiimov
        
        // SCAR-20 (ID: 38)
        case 38: 
            return { 175, 0.1f, 6969, 1 }; // Cardiac
        
        // SG 553 (ID: 39)
        case 39: 
            return { 151, 0.1f, 6969, 1 }; // Cyrex
        
        // SSG 08 (ID: 40)
        case 40: 
            return { 179, 0.1f, 6969, 1 }; // Blood in the Water
        
        // Knife (ID: 42)
        case 42: 
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        // Knife T (ID: 59)
        case 59: 
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        // M4A1-S (ID: 60)
        case 60: 
            return { 619, 0.1f, 6969, 1 }; // Hyper Beast
        
        // USP-S (ID: 61)
        case 61: 
            return { 504, 0.1f, 6969, 1 }; // Kill Confirmed
        
        // CZ75-Auto (ID: 63)
        case 63: 
            return { 220, 0.1f, 6969, 1 }; // The Fuschia Is Now
        
        // R8 Revolver (ID: 64)
        case 64: 
            return { 415, 0.1f, 6969, 1 }; // Fade
        
        // ID 71 (utilitário/item especial)
        case 71:
            return { 0, 0.0f, 0, 0 }; // Sem skin

        // ID 132 (item especial)
        case 132:
            return { 0, 0.0f, 0, 0 }; // Sem skin

        // ID 322 (faca especial)
        case 322:
            return { 38, 0.1f, 6969, 1 }; // Fade

        // IDs adicionais baseados no debug output
        case 376:
            return { 38, 0.1f, 6969, 1 }; // Weapon ID 376 - Fade skin

        case 1675:
            return { 0, 0.0f, 0, 0 }; // Utilitário

        case 4111:
            return { 0, 0.0f, 0, 0 }; // Utilitário
        
        // Facas especiais
        case 500: // Bayonet
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 505: // Flip Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 506: // Gut Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 507: // Karambit
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 508: // M9 Bayonet
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 509: // Huntsman Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 512: // Falchion Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 514: // Bowie Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 515: // Butterfly Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 516: // Shadow Daggers
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        case 526: // Kukri Knife
            return { 38, 0.1f, 6969, 1 }; // Fade
        
        // Granadas e utilitários (sem skins)
        case 43: // Flashbang
        case 44: // HE Grenade
        case 45: // Smoke Grenade
        case 46: // Molotov
        case 47: // Decoy
        case 48: // Incendiary Grenade
        case 49: // C4
            return { 0, 0.0f, 0, 0 }; // Sem skin para utilitários
        
        default:
            return { 0, 0.0f, 0, 0 }; // Sem skin
    }
}
